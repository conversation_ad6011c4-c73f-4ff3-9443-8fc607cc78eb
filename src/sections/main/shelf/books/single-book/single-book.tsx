import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import { Box, Chip, Stack, IconButton, Typography } from '@mui/material';

import { useRouter } from 'src/routes/hooks';

import { useBoolean } from 'src/hooks/use-boolean';

import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';

import { DonateBy } from 'src/sections/main/shelf/books/single-book/donated-by';

import TakeHome from '../section/take-home';

const SingleBook = () => {
  const takeHomeDialog = useBoolean();
  const isAvailableToRead = useBoolean(true);

  const navigate = useRouter();

  const bookDetails = {
    id: '1',
    name: '1984',
    author: '<PERSON>',
  };

  const handleReadNow = async () => {
    const promise = new Promise((resolve) => setTimeout(resolve, 3000));

    try {
      toast.promise(promise, {
        loading: 'Reserving book...',
        success: () => `Book reserved!`,
        error: 'Error',
        closeButton: false,
      });

      await promise;
      // TODO: REDIRECT THE USER TO THE MY BOOKS PAGE
      isAvailableToRead.onFalse();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Stack
      gap={2}
      minHeight={{
        xs: 'calc(100svh - var(--layout-header-mobile-height) - 16px)',
        md: 'calc(100svh - var(--layout-header-desktop-height) - 16px)',
      }}
    >
      <Box gap={2} display="flex" alignItems="center" minWidth={0}>
        <IconButton aria-label="go back" onClick={() => navigate.back()}>
          <Iconify icon="mdi:arrow-left" />
        </IconButton>
        <Avatar alt="Book" sx={{ width: 48, height: 48 }}>
          <Iconify icon="mdi:book-open-variant-outline" width={24} />
        </Avatar>
        <Stack sx={{ minWidth: 0 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', minWidth: 0 }}>
            <Typography
              sx={{
                textTransform: 'capitalize',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
                minWidth: 0,
              }}
              fontWeight="bold"
              variant="h5"
            >
              {/* {item.name} */}
              Book Name
            </Typography>
          </Box>
          <Box
            display="flex"
            gap="4px"
            alignItems="center"
            sx={{ color: (theme) => theme.palette.grey[500] }}
          >
            <Iconify width="16" height="16" icon="mdi:account" />
            <Typography
              sx={{
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
              }}
              variant="body1"
              fontSize="14px"
            >
              {/* {item.author} */}
              Author Name
            </Typography>
          </Box>
        </Stack>
      </Box>
      <Chip
        label="fiction"
        size="small"
        variant="outlined"
        sx={{
          width: 'min-content',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
        }}
      />
      <Typography variant="caption" sx={{ fontSize: '16px', textAlign: 'start' }}>
        Lorem ipsum dolor sit amet consectetur. Commodo tellus vulputate suspendisse vel in orci
        ultrices augue. Eget fringilla eget vitae nunc tincidunt congue posuere interdum ultricies.
        Pharetra vitae enim blandit felis ante. Mattis proin odio lacus arcu maecenas. Lorem ipsum
        dolor sit amet consectetur. Commodo tellus vulputate suspendisse vel in orci ultrices augue.
        Eget fringilla eget vitae nunc tincidunt congue posuere interdum ultricies. Pharetra vitae
        enim blandit felis ante. Mattis proin odio lacus arcu maecenas.
      </Typography>
      <Box gap={2} display="flex" flex={1} sx={{ mt: 2 }}>
        <Button
          onClick={handleReadNow}
          disabled={!isAvailableToRead}
          fullWidth
          variant="soft"
          size="large"
          color="warning"
        >
          Read Now
        </Button>
        <Button
          onClick={() => takeHomeDialog.onTrue()}
          fullWidth
          variant="soft"
          size="large"
          color="secondary"
        >
          Take Home
        </Button>
      </Box>
      <DonateBy name="John Doe" />
      <TakeHome takeHomeDialogState={takeHomeDialog} bookToTake={bookDetails} />
    </Stack>
  );
};

export default SingleBook;
