import type { IUseBooleanReturn } from 'src/hooks/use-boolean';

import React from 'react';

import { LoadingButton } from '@mui/lab';
import { Box, Stack, Avatar, useTheme, Typography, IconButton } from '@mui/material';

import { Iconify } from 'src/components/iconify';

import Success from '../take-home-dialog/success';

const ConfirmView = ({
  chosenBookDetails,
  bookToTake,
  confirm,
  onClose,
}: {
  chosenBookDetails: {
    id: number;
    name: string;
    author: string;
  };
  bookToTake: {
    id: string;
    name: string;
    author: string;
  };
  confirm: IUseBooleanReturn;
  onClose: () => void;
}) => {
  const theme = useTheme();

  const isSubmitting = false;
  return (
    <>
      <Box
        sx={{
          p: { xs: 2, sm: 3 },
          pb: 2,
          flexShrink: 0,
          borderBottom: `1px solid ${theme.palette.divider}`,
          bgcolor: theme.palette.background.paper,
        }}
      >
        <Stack
          direction="row"
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 1,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <IconButton
              aria-label="go back"
              onClick={() => confirm.onFalse()}
              sx={{
                color: theme.palette.text.secondary,
                '&:hover': {
                  color: theme.palette.text.primary,
                  backgroundColor: theme.palette.action.hover,
                },
              }}
            >
              <Iconify icon="mdi:arrow-left" width={24} />
            </IconButton>

            <Typography
              variant="h6"
              sx={{
                fontWeight: 600,
                fontSize: { xs: '16px', sm: '18px' },
                color: theme.palette.text.primary,
              }}
            >
              Confirm Your Exchange
            </Typography>
          </Box>
        </Stack>

        <Typography
          variant="body2"
          sx={{
            color: theme.palette.text.secondary,
            fontSize: '14px',
            lineHeight: 1.6,
          }}
        >
          Fill in the details to add your book to the exchange
        </Typography>
      </Box>
      <Box display="flex" my={3} justifyContent="center" alignItems="center" gap={2}>
        <Book name={chosenBookDetails.name} author={chosenBookDetails.author} />
        <Iconify icon="mdi:arrow-right" width={24} />
        <Book name={bookToTake.name} author={bookToTake.author} isGivingBook={false} />
      </Box>
      <Success />
      <LoadingButton
        color="primary"
        type="submit"
        variant="contained"
        loading={isSubmitting}
        size="large"
        startIcon={!isSubmitting ? <Iconify icon="mdi:check" width={18} /> : null}
        sx={{
          textTransform: 'none',
          fontWeight: 600,
          minHeight: 48,
          boxShadow: theme.shadows[4],
          '&:hover': {
            boxShadow: theme.shadows[8],
          },
          mt: 'auto',
        }}
      >
        {isSubmitting ? 'Confirming Exchange...' : 'Confirm & Take Home'}
      </LoadingButton>
    </>
  );
};

export default ConfirmView;

function Book({
  author,
  name,
  isGivingBook = true,
}: {
  name: string;
  author: string;
  isGivingBook?: boolean;
}) {
  return (
    <Stack alignItems="center" sx={{ width: 1 / 2 }} gap={2}>
      <Typography variant="subtitle1">
        {isGivingBook ? "You're Giving" : "You're Taking"}
      </Typography>
      <Avatar alt="Book" sx={{ width: 48, height: 48 }}>
        <Iconify icon="mdi:book-open-variant-outline" width={24} />
      </Avatar>
      <Typography variant="h6">{name}</Typography>
      <Typography
        sx={{
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
        }}
        variant="body1"
        fontSize="14px"
      >
        {author}
      </Typography>
    </Stack>
  );
}
