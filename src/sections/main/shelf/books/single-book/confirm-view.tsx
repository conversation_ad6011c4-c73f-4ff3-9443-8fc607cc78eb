import React from 'react';

import { LoadingButton } from '@mui/lab';
import { Box, Stack, Avatar, useTheme, Typography } from '@mui/material';

import { useBoolean, type IUseBooleanReturn } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';

import Success from '../take-home-dialog/success';
import ContentHeader from '../take-home-dialog/content-header';

const ConfirmView = ({
  chosenBookDetails,
  bookToTake,
  confirm,
  onClose,
}: {
  chosenBookDetails: {
    id: number;
    name: string;
    author: string;
  };
  bookToTake: {
    id: string;
    name: string;
    author: string;
  };
  confirm: IUseBooleanReturn;
  onClose: () => void;
}) => {
  const theme = useTheme();

  const isSubmitting = useBoolean();

  const confirmed = useBoolean();

  const handleSubmit = () => {
    isSubmitting.onTrue();
    setTimeout(() => {
      isSubmitting.onFalse();
      confirmed.onTrue();
      setTimeout(() => {
        onClose();
      }, 2000);
    }, 1000);
  };

  return (
    <>
      <ContentHeader
        header="Confirm Your Exchange"
        subtitle="Confirm the details of your book exchange"
        onClickBackButton={confirm.onFalse}
      />
      {confirmed.value === false ? (
        <Box display="flex" my={3} justifyContent="center" alignItems="center" gap={2}>
          <Book name={chosenBookDetails.name} author={chosenBookDetails.author} />
          <Iconify icon="mdi:arrow-right" width={24} />
          <Book name={bookToTake.name} author={bookToTake.author} isGivingBook={false} />
        </Box>
      ) : (
        <Success />
      )}
      <LoadingButton
        color="primary"
        type="submit"
        variant="contained"
        onClick={handleSubmit}
        loading={isSubmitting.value}
        disabled={confirmed.value}
        size="large"
        startIcon={!isSubmitting.value ? <Iconify icon="mdi:check" width={18} /> : null}
        sx={{
          textTransform: 'none',
          fontWeight: 600,
          minHeight: 48,
          boxShadow: theme.shadows[4],
          '&:hover': {
            boxShadow: theme.shadows[8],
          },
          mt: 'auto',
        }}
      >
        {isSubmitting.value ? 'Confirming Exchange...' : 'Confirm & Take Home'}
      </LoadingButton>
    </>
  );
};

export default ConfirmView;

function Book({
  author,
  name,
  isGivingBook = true,
}: {
  name: string;
  author: string;
  isGivingBook?: boolean;
}) {
  return (
    <Stack
      alignItems="center"
      sx={{ width: 1 / 2 }}
      gap={2}
      textAlign="center"
      overflow="hidden"
      textOverflow="ellipsis"
    >
      <Typography variant="subtitle1">
        {isGivingBook ? "You're Giving" : "You're Taking"}
      </Typography>
      <Avatar alt="Book" sx={{ width: 48, height: 48 }}>
        <Iconify icon="mdi:book-open-variant-outline" width={24} />
      </Avatar>
      <Typography variant="h6">
        {name}
        {name}
        {name}
        fasdfasdfasdfsadfjsdafalkfjaks
      </Typography>
      <Typography
        sx={{
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
        }}
        variant="body1"
        fontSize="14px"
      >
        {author}
      </Typography>
    </Stack>
  );
}
