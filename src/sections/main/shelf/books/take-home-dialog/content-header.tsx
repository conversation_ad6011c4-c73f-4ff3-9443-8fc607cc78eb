import React from 'react';

import { Box, Stack, Divider, Typography, IconButton } from '@mui/material';

import { Iconify } from 'src/components/iconify';

const ContentHeader = ({
  header,
  subtitle,
  onClickBackButton,
}: {
  header: string;
  subtitle: string;
  onClickBackButton: () => void;
}) => (
  <Box
    sx={{
      mt: 1,
      display: 'flex',
      flexDirection: 'column',
      gap: 1,
      flexShrink: 0,
    }}
  >
    <Stack
      direction="row"
      sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <IconButton
          aria-label="go back"
          onClick={() => {
            console.log('close button clicked');
            console.log('🚀 ~ onClickBackButton:', onClickBackButton);

            onClickBackButton();
          }}
        >
          <Iconify icon="mdi:arrow-left" width={24} />
        </IconButton>

        <Typography variant="h5">{header}</Typography>
      </Box>
    </Stack>

    <Typography variant="body2" color="text.secondary">
      {subtitle}
    </Typography>
    <Divider sx={{ mx: -2, borderStyle: 'dashed' }} />
  </Box>
);

export default ContentHeader;
