import type { BoxProps } from '@mui/material';
import type { TransitionProps } from '@mui/material/transitions';

import { useState, forwardRef } from 'react';

import { Slide, Dialog, useTheme } from '@mui/material';

import { useBoolean, type IUseBooleanReturn } from 'src/hooks/use-boolean';

import { CompactContent } from 'src/layouts/public';

import ConfirmView from '../single-book/confirm-view';
import BooksList from '../take-home-dialog/book-list';
import DialogTop from '../take-home-dialog/dialog-top';
import NewBookForm from '../take-home-dialog/new-book-form';
import AddNewBookButton from '../take-home-dialog/add-new-book-button';
import DialogContentWrapper from '../take-home-dialog/dialog-content-wrapper';

type Props = {
  takeHomeDialogState: IUseBooleanReturn;
  bookToTake: {
    id: string;
    name: string;
    author: string;
  };
};

const Transition = forwardRef(
  (
    props: TransitionProps & {
      children: React.ReactElement;
    },
    ref: React.Ref<unknown>
  ) => <Slide direction="up" ref={ref} {...props} />
);

const books = [
  { id: '1', name: 'The Midnight Library', author: 'Matt Haig' },
  { id: '2', name: 'Atomic Habits', author: 'James Clear' },
  { id: '3', name: 'The Psychology of Money', author: 'Morgan Housel' },
  { id: '4', name: 'Educated', author: 'Tara Westover' },
  { id: '5', name: 'The Silent Patient', author: 'Alex Michaelides' },
  { id: '6', name: 'Where the Crawdads Sing', author: 'Delia Owens' },
  { id: '7', name: 'The Seven Husbands of Evelyn Hugo', author: 'Taylor Jenkins Reid' },
  { id: '8', name: 'Becoming', author: 'Michelle Obama' },
];

const TakeHome = ({ takeHomeDialogState, bookToTake, sx }: Props & BoxProps) => {
  const theme = useTheme();

  const confirmSection = useBoolean();
  const newBook = useBoolean();

  const [chosenBookDetails, setChosenBookDetails] = useState<
    | ((typeof books)[number] & {
        categoryId?: string;
        description?: string;
      })
    | null
  >(null);

  const handleChoseOrCreateExchangeBook = (bookDetails: any) => {
    setChosenBookDetails(bookDetails);
    confirmSection.onTrue();
  };

  const handleNewBookFormClick = () => newBook.onTrue();

  const handleCloseNewBookForm = () => newBook.onFalse();

  const handleClose = () => {
    takeHomeDialogState.onFalse();
    newBook.onFalse(); // Reset new book form when closing main dialog
  };

  // Sample book data - you can replace with your actual data

  return (
    <Dialog
      open={takeHomeDialogState.value}
      fullScreen
      onClose={handleClose}
      aria-labelledby="book-exchange-modal"
      aria-describedby="select-book-to-exchange"
      TransitionComponent={Transition}
      sx={{
        bgcolor: theme.palette.background.default,
      }}
    >
      <CompactContent maxHeight="100dvh" py={2}>
        <DialogTop onClose={handleClose} />

        {!chosenBookDetails && !confirmSection.value ? (
          // Book selecting section choose/create
          !newBook.value ? (
            <DialogContentWrapper
              bottomBar={<AddNewBookButton onNewBookFormClick={handleNewBookFormClick} />}
            >
              <BooksList books={books} onSelectBookToExchange={handleChoseOrCreateExchangeBook} />
            </DialogContentWrapper>
          ) : (
            <NewBookForm
              confirm={confirmSection}
              onCloseNewBookForm={handleCloseNewBookForm}
              onCreateExchangeBook={handleChoseOrCreateExchangeBook}
              chosenBookDetails={chosenBookDetails}
            />
          )
        ) : (
          // confirm section
          <ConfirmView
            chosenBookDetails={chosenBookDetails as any}
            bookToTake={bookToTake}
            onClose={handleClose}
            confirm={confirmSection}
          />
        )}
      </CompactContent>
    </Dialog>
  );
};

export default TakeHome;
